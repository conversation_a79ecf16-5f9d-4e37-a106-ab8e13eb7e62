import pyecharts.options as opts
from pyecharts.charts import Geo
from pyecharts.globals import ChartType

# 1. 机场坐标数据 (同上)
airport_coords = {
    'ZWHM': ['武汉天河', 114.207, 30.776], 'ZBDH': ['秦皇岛北戴河', 119.516, 39.795],
    'ZBLA': ['呼伦贝尔海拉尔', 119.845, 49.205], 'ZSHC': ['杭州萧山', 120.434, 30.229],
    'ZPLJ': ['丽江三义', 100.246, 26.683], 'ZBHH': ['呼和浩特白塔', 111.821, 40.852],
    'ZSSS': ['上海虹桥', 121.336, 31.198], 'ZBAD': ['北京大兴', 116.410, 39.509],
    'ZWSH': ['喀什徕宁', 76.022, 39.543], 'ZHCC': ['海口美兰', 110.459, 19.939],
    'ZJHK': ['西双版纳嘎洒', 100.762, 21.974], 'ZSJN': ['济南遥墙', 117.216, 36.857],
    'ZWFY': ['富蕴可可托海', 89.516, 46.800], 'ZYDQ': ['大庆萨尔图', 124.933, 46.750],
    'ZWTL': ['吐鲁番交河', 89.093, 43.030], 'ZWQT': ['奇台江布拉克', 89.600, 44.150],
    'ZWYT': ['伊宁', 81.331, 43.955], 'ZWYN': ['莎车叶尔羌', 77.271, 38.238],
    'ZYMH': ['漠河古莲', 122.400, 52.915]
}

# 2. 风险数据和量化模型
# 格式: (ICAO, 风险项, 排名) -> 排名1得3分, 2得2分, 3得1分
risk_data = {
    '空客': [
        ('ZWHM', '非精密进近', 1), ('ZBDH', '非精密进近', 2), ('ZBLA', '非精密进近', 3),
        ('ZSHC', '高进近', 1), ('ZPLJ', '高进近', 2), ('ZBHH', '高进近', 3),
        ('ZSSS', '平均载荷', 1), ('ZBAD', '平均载荷', 2), ('ZWSH', '平均载荷', 3),
        ('ZHCC', '大侧风/乱流', 1), ('ZJHK', '大侧风/乱流', 2), ('ZSJN', '大侧风/乱流', 3),
    ],
    'C909': [
        ('ZWFY', '非精密进近', 1), ('ZWSH', '非精密进近', 2), ('ZYDQ', '非精密进近', 3),
        ('ZWTL', '高进近', 1), ('ZWSH', '高进近', 2), ('ZWQT', '高进近', 3),
        ('ZWYT', '平均载荷', 1), ('ZYDQ', '平均载荷', 2), ('ZWFY', '平均载荷', 3),
        ('ZWTL', '大侧风/乱流', 1), ('ZWYN', '大侧风/乱流', 2), ('ZYMH', '大侧风/乱流', 3),
    ]
}

points_map = {1: 3, 2: 2, 3: 1}

def calculate_risk_scores(aircraft_type):
    scores = {}
    details = {}
    for code, risk_item, rank in risk_data[aircraft_type]:
        score = points_map[rank]
        scores[code] = scores.get(code, 0) + score
        if code not in details:
            details[code] = []
        details[code].append(f"{risk_item}(第{rank}名)")
    
    # 转换成 pyecharts Geo 需要的格式
    data_pairs = []
    for code, total_score in scores.items():
        airport_name = airport_coords[code][0]
        detail_str = " | ".join(details[code])
        # 使用正确的数据格式：[name, value]
        data_pairs.append([airport_name, total_score])
    
    return data_pairs, max(scores.values())


def create_risk_map(aircraft_type: str, data_pairs: list, max_score: int):
    c = Geo()

    # 首先添加自定义坐标点
    for code, info in airport_coords.items():
        airport_name, lng, lat = info
        c.add_coordinate(airport_name, lng, lat)

    c = (
        c.add_schema(maptype="china", itemstyle_opts=opts.ItemStyleOpts(color="#f2f2f2", border_color="#aaa"))
        .add(
            series_name="机场风险",
            data_pair=data_pairs,
            type_=ChartType.EFFECT_SCATTER, # 使用带涟漪效果的散点图
            symbol_size=12,
        )
        .set_series_opts(label_opts=opts.LabelOpts(is_show=False))
        .set_global_opts(
            title_opts=opts.TitleOpts(
                title=f"{aircraft_type} 机型技术难度机场分布图",
                subtitle="风险指数越高，圆点越大颜色越深，需重点关注",
                pos_left="center",
                title_textstyle_opts=opts.TextStyleOpts(font_size=20)
            ),
            visualmap_opts=opts.VisualMapOpts(
                is_calculable=True,
                range_color=['#98FB98', '#FFD700', '#FF4500'], # 绿 -> 黄 -> 红
                min_=1,
                max_=max_score,
                pos_left="10%",
                pos_top="bottom"
            ),
            tooltip_opts=opts.TooltipOpts(
                formatter="{b}: {c}"  # 简化tooltip格式
            )
        )
    )
    return c

# --- 主程序：生成两张地图 ---

# 1. 生成空客机型风险地图
airbus_data, airbus_max_score = calculate_risk_scores('空客')
airbus_map = create_risk_map('空客', airbus_data, airbus_max_score)
airbus_map.render("airbus_risk_map.html")
print("已生成空客机型风险地图: airbus_risk_map.html")

# 2. 生成C909机型风险地图
c909_data, c909_max_score = calculate_risk_scores('C909')
c909_map = create_risk_map('C909', c909_data, c909_max_score)
c909_map.render("c909_risk_map.html")
print("已生成C909机型风险地图: c909_risk_map.html")
